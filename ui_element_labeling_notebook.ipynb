# Install required packages
!pip install google-generativeai pillow opencv-python matplotlib pandas numpy

# Import necessary libraries
import json
import os
import base64
import io
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import google.generativeai as genai
from google.colab import files, userdata
import warnings
warnings.filterwarnings('ignore')

# Configure Gemini AI
# You need to add your Gemini API key to Colab secrets with name 'GEMINI_API_KEY'
try:
    GEMINI_API_KEY = userdata.get('GEMINI_API_KEY')
    genai.configure(api_key=GEMINI_API_KEY)
    print("✅ Gemini AI configured successfully")
except Exception as e:
    print("❌ Please add your Gemini API key to Colab secrets with name 'GEMINI_API_KEY'")
    print("Go to the key icon on the left sidebar and add your API key")
    GEMINI_API_KEY = input("Or enter your Gemini API key here: ")
    genai.configure(api_key=GEMINI_API_KEY)

# Initialize the model
model = genai.GenerativeModel('gemini-1.5-flash')
print("✅ Gemini model initialized")

# Load bounding box data
with open(bounding_box_file, 'r') as f:
    bounding_boxes = json.load(f)

print(f"📊 Loaded {len(bounding_boxes)} bounding boxes")

# Load screenshot
screenshot = Image.open(screenshot_file)
print(f"🖼️ Screenshot dimensions: {screenshot.size}")

# Load available labels
with open(labels_file, 'r') as f:
    available_labels = [line.strip() for line in f.readlines() if line.strip()]

print(f"🏷️ Available labels: {len(available_labels)}")
print("First 10 labels:", available_labels[:10])

# Filter meaningful UI elements (remove very small or very large elements)
def filter_meaningful_elements(bounding_boxes, min_area=100, max_area_ratio=0.8):
    """
    Filter bounding boxes to keep only meaningful UI elements
    """
    screenshot_area = screenshot.size[0] * screenshot.size[1]
    max_area = screenshot_area * max_area_ratio
    
    filtered_elements = []
    
    for element in bounding_boxes:
        area = element['width'] * element['height']
        
        # Filter criteria
        if (area >= min_area and 
            area <= max_area and 
            element['width'] > 10 and 
            element['height'] > 10 and
            element['tag'] not in ['html', 'body']):
            
            filtered_elements.append(element)
    
    return filtered_elements

# Apply filtering
filtered_elements = filter_meaningful_elements(bounding_boxes)
print(f"🔍 Filtered to {len(filtered_elements)} meaningful UI elements")

# Sort by area (largest first) and take top 50 for processing
filtered_elements.sort(key=lambda x: x['width'] * x['height'], reverse=True)
elements_to_process = filtered_elements[:50]
print(f"📋 Processing top {len(elements_to_process)} elements")

def extract_element_image(screenshot, element, padding=5):
    """
    Extract a close-up image of a UI element from the screenshot
    """
    x, y, width, height = element['x'], element['y'], element['width'], element['height']
    
    # Add padding and ensure bounds
    x1 = max(0, x - padding)
    y1 = max(0, y - padding)
    x2 = min(screenshot.size[0], x + width + padding)
    y2 = min(screenshot.size[1], y + height + padding)
    
    # Extract the region
    element_image = screenshot.crop((x1, y1, x2, y2))
    
    return element_image

def create_annotated_screenshot(screenshot, elements, max_elements=20):
    """
    Create an annotated screenshot showing bounding boxes
    """
    annotated = screenshot.copy()
    draw = ImageDraw.Draw(annotated)
    
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'yellow', 'cyan', 'magenta']
    
    for i, element in enumerate(elements[:max_elements]):
        x, y, width, height = element['x'], element['y'], element['width'], element['height']
        color = colors[i % len(colors)]
        
        # Draw bounding box
        draw.rectangle([x, y, x + width, y + height], outline=color, width=2)
        
        # Draw element number
        draw.text((x, y-15), str(i+1), fill=color)
    
    return annotated

# Create annotated screenshot
annotated_screenshot = create_annotated_screenshot(screenshot, elements_to_process)
plt.figure(figsize=(15, 10))
plt.imshow(annotated_screenshot)
plt.title("Annotated Screenshot with UI Elements")
plt.axis('off')
plt.show()

print("🎯 Annotated screenshot created showing top UI elements")

def prepare_element_data_for_gemini(element):
    """
    Prepare element metadata for Gemini analysis
    """
    metadata = {
        'coordinates': f"x:{element['x']}, y:{element['y']}, width:{element['width']}, height:{element['height']}",
        'tag': element['tag'],
        'class': element.get('class', ''),
        'id': element.get('id', ''),
        'selector': element.get('selector', ''),
        'area': element['width'] * element['height']
    }
    return metadata

def analyze_element_with_gemini(element_image, metadata, available_labels):
    """
    Use Gemini AI to analyze UI element and assign appropriate label
    """
    # Convert PIL image to bytes for Gemini
    img_byte_arr = io.BytesIO()
    element_image.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()
    
    # Create prompt for Gemini
    prompt = f"""
    Analyze this UI element and assign the most appropriate label from the provided list.
    
    Element Metadata:
    - Coordinates: {metadata['coordinates']}
    - HTML Tag: {metadata['tag']}
    - CSS Class: {metadata['class']}
    - ID: {metadata['id']}
    - CSS Selector: {metadata['selector']}
    - Area: {metadata['area']} pixels
    
    Available Labels:
    {', '.join(available_labels[:50])}  # Show first 50 labels
    
    Instructions:
    1. Analyze the visual appearance of the UI element in the image
    2. Consider the HTML tag, CSS classes, and other metadata
    3. Choose the MOST SPECIFIC and APPROPRIATE label from the available labels list
    4. If multiple labels could apply, choose the most specific one
    5. Consider the element's likely function and user interaction
    
    Respond with ONLY the chosen label name, nothing else.
    If no label fits well, respond with "Unknown".
    """
    
    try:
        # Upload image and get response
        response = model.generate_content([
            prompt,
            {
                "mime_type": "image/png",
                "data": img_byte_arr
            }
        ])
        
        predicted_label = response.text.strip()
        
        # Validate that the predicted label is in our available labels
        if predicted_label in available_labels:
            return predicted_label
        else:
            # Try to find a close match
            for label in available_labels:
                if predicted_label.lower() in label.lower() or label.lower() in predicted_label.lower():
                    return label
            return "Unknown"
            
    except Exception as e:
        print(f"Error analyzing element: {e}")
        return "Error"

print("🤖 Gemini analysis functions ready")

# Process elements in batches
results = []
batch_size = 10  # Process 10 elements at a time to avoid rate limits

print("🔄 Starting batch processing of UI elements...")
print(f"Processing {len(elements_to_process)} elements in batches of {batch_size}")

for i in range(0, len(elements_to_process), batch_size):
    batch = elements_to_process[i:i+batch_size]
    print(f"\n📦 Processing batch {i//batch_size + 1}/{(len(elements_to_process)-1)//batch_size + 1}")
    
    for j, element in enumerate(batch):
        element_idx = i + j + 1
        print(f"  🔍 Analyzing element {element_idx}/{len(elements_to_process)}...", end=" ")
        
        # Extract element image
        element_image = extract_element_image(screenshot, element)
        
        # Prepare metadata
        metadata = prepare_element_data_for_gemini(element)
        
        # Analyze with Gemini
        predicted_label = analyze_element_with_gemini(element_image, metadata, available_labels)
        
        # Store results
        result = {
            'element_id': element_idx,
            'coordinates': metadata['coordinates'],
            'tag': metadata['tag'],
            'class': metadata['class'],
            'id': metadata['id'],
            'selector': metadata['selector'],
            'area': metadata['area'],
            'predicted_label': predicted_label,
            'element_image': element_image
        }
        
        results.append(result)
        print(f"✅ {predicted_label}")
        
        # Small delay to respect API rate limits
        import time
        time.sleep(1)

print(f"\n🎉 Completed processing {len(results)} elements!")

# Create DataFrame for analysis
df_results = pd.DataFrame([{
    'element_id': r['element_id'],
    'coordinates': r['coordinates'],
    'tag': r['tag'],
    'class': r['class'][:50] + '...' if len(r['class']) > 50 else r['class'],  # Truncate long class names
    'id': r['id'],
    'area': r['area'],
    'predicted_label': r['predicted_label']
} for r in results])

print("📊 Results Summary:")
print(f"Total elements processed: {len(df_results)}")
print(f"Unique labels assigned: {df_results['predicted_label'].nunique()}")
print(f"Most common labels:")
print(df_results['predicted_label'].value_counts().head(10))

# Display results table
print("\n📋 Detailed Results:")
display(df_results.head(20))

# Visualize sample results with images
def display_sample_results(results, num_samples=12):
    """
    Display a grid of sample UI elements with their predicted labels
    """
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    axes = axes.flatten()
    
    sample_results = results[:num_samples]
    
    for i, result in enumerate(sample_results):
        if i >= len(axes):
            break
            
        ax = axes[i]
        ax.imshow(result['element_image'])
        ax.set_title(f"#{result['element_id']}: {result['predicted_label']}\n{result['tag']}", 
                    fontsize=10, pad=10)
        ax.axis('off')
    
    # Hide unused subplots
    for i in range(len(sample_results), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.suptitle("Sample UI Elements with Predicted Labels", fontsize=16, y=1.02)
    plt.show()

display_sample_results(results)
print("🖼️ Sample results visualization complete")

# Export results to various formats
import datetime

timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# 1. Export to CSV
csv_filename = f"ui_labeling_results_{timestamp}.csv"
df_results.to_csv(csv_filename, index=False)
print(f"📄 Results exported to CSV: {csv_filename}")

# 2. Export to JSON with full metadata
json_results = []
for result in results:
    json_result = {
        'element_id': result['element_id'],
        'coordinates': result['coordinates'],
        'tag': result['tag'],
        'class': result['class'],
        'id': result['id'],
        'selector': result['selector'],
        'area': result['area'],
        'predicted_label': result['predicted_label']
    }
    json_results.append(json_result)

json_filename = f"ui_labeling_results_{timestamp}.json"
with open(json_filename, 'w') as f:
    json.dump(json_results, f, indent=2)
print(f"📄 Results exported to JSON: {json_filename}")

# 3. Create summary report
summary_filename = f"ui_labeling_summary_{timestamp}.txt"
with open(summary_filename, 'w') as f:
    f.write(f"UI Element Labeling Summary Report\n")
    f.write(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    f.write(f"Total elements processed: {len(results)}\n")
    f.write(f"Unique labels assigned: {df_results['predicted_label'].nunique()}\n\n")
    f.write("Label distribution:\n")
    for label, count in df_results['predicted_label'].value_counts().items():
        f.write(f"  {label}: {count}\n")
    f.write("\nTop elements by area:\n")
    top_elements = df_results.nlargest(10, 'area')
    for _, row in top_elements.iterrows():
        f.write(f"  Element #{row['element_id']}: {row['predicted_label']} ({row['area']} px²)\n")

print(f"📄 Summary report exported: {summary_filename}")

# 4. Download files
print("\n📥 Downloading files...")
files.download(csv_filename)
files.download(json_filename)
files.download(summary_filename)

print("\n✅ All files exported and downloaded successfully!")

# Advanced analysis and insights
print("🔍 Advanced Analysis:")

# 1. Label distribution by HTML tag
print("\n📊 Label distribution by HTML tag:")
tag_label_crosstab = pd.crosstab(df_results['tag'], df_results['predicted_label'])
print(tag_label_crosstab)

# 2. Element size analysis
print("\n📏 Element size analysis:")
size_stats = df_results.groupby('predicted_label')['area'].agg(['count', 'mean', 'median', 'std']).round(2)
print(size_stats.head(10))

# 3. Create visualization of label distribution
plt.figure(figsize=(12, 8))
label_counts = df_results['predicted_label'].value_counts().head(15)
plt.barh(range(len(label_counts)), label_counts.values)
plt.yticks(range(len(label_counts)), label_counts.index)
plt.xlabel('Count')
plt.title('Top 15 Most Common UI Element Labels')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

# 4. Confidence analysis (elements labeled as "Unknown" or "Error")
uncertain_labels = df_results[df_results['predicted_label'].isin(['Unknown', 'Error'])]
print(f"\n❓ Elements with uncertain labels: {len(uncertain_labels)} ({len(uncertain_labels)/len(df_results)*100:.1f}%)")

if len(uncertain_labels) > 0:
    print("Uncertain elements by tag:")
    print(uncertain_labels['tag'].value_counts())

print("\n🎯 Analysis complete!")